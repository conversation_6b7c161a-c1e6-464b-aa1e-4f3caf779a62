services:

  xrss:
    image: vdematos/xrss:latest
    expose:
      - "8000"
    environment:
      - REDIS_URL=redis://redis:6379
      - TWITTER_USERNAME=${TWITTER_USERNAME}
      - TWITTER_EMAIL=${TWITTER_EMAIL}
      - TWITTER_PASSWORD=${TWITTER_PASSWORD}
    depends_on:
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/docs"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:alpine
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  redis_data:
