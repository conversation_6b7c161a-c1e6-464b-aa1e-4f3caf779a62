[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "xrss"
version = "0.1.0"
description = "RSS feed generator for X/Twitter"
readme = "README.md"
requires-python = ">=3.10"
license = { file = "LICENSE" }
authors = [
    { name = "thytu" }
]
dependencies = [
    "fastapi>=0.104.1",
    "uvicorn>=0.24.0",
    "twikit>=0.3.0",
    "feedgen>=0.9.0",
    "python-dotenv>=1.0.0",
    "pydantic>=2.5.2",
    "pydantic-settings>=2.1.0",
    "requests>=2.31.0",
    "redis>=5.0.1",
    "aioredis>=2.0.1",
    "asyncio>=3.4.3",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.21.1",
    "pytest-mock>=3.12.0",
    "pytest-env>=1.1.1",
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "pylint>=3.0.2",
    "types-redis>=4.6.0.20231224",
    "types-requests>=2.31.0.20231231",
    "mkdocs>=1.5.3",
    "mkdocs-material>=9.5.2",
]

[tool.black]
line-length = 100
target-version = ["py39"]
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
line_length = 100

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
ignore_missing_imports = true
follow_imports = "skip"

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_functions = ["test_*"]
addopts = "--verbose --cov=xrss --cov-report=term-missing"
asyncio_mode = "auto"

[tool.coverage.run]
source = ["xrss"]
omit = ["tests/*", "setup.py"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "pass",
    "raise ImportError",
]
